<template>
  <base-echart id="bar-line-echart" class="bar-line-echart" :width="width" :height="height" :option="options" />
</template>

<script>
  import BaseEchart from '@/components/Echarts/BaseEchart.vue'
  import * as echarts from 'echarts/core'

  export default {
    components: {
      BaseEchart,
    },
    props: {
      dataSource: {
        require: true,
        default: () => [
          {
            name: '',
            data: [],
          },
        ],
      },
      width: { default: '100%' },
      height: { default: '100%' },
    },
    yAxis: { default: null },
    data() {
      return {}
    },
    computed: {
      options() {
        return this.getOptions(this.dataSource)
      },
    },

    methods: {
      getOptions(dataSource) {
        const option = {
          grid: [
            // 配置第一个柱状图的位置
            {
              left: '10%',
              right: '10%',
              top: '11%',
              height: '35%',
            },
            // 配置第二个折线图位置
            {
              left: '10%',
              right: '10%',
              top: '53%',
              height: '35%',
            },
          ],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              label: {
                backgroundColor: '#6a7985',
                height: 10,
              },
              crossStyle: { color: '#1664FF' },
              lineStyle: { color: '#1664FF' },
            },

            // formatter函数动态修改tooltip样式
            formatter: function (params) {
              if (params) {
                let htmlStr = ''
                htmlStr += params[0].name.replace(/\-/g, '/') + '<br/>' //x轴的名称
                for (var i = 0; i < params.length; i++) {
                  let param = params[i] // 存一份item项
                  let seriesName = param.seriesName //图例名称
                  let value = param.value[1] === null ? '-' : param.value[1] //y轴值
                  let color = param.color //图例颜色

                  function getUnit(seriesName) {
                    switch (seriesName) {
                      case '时段雨量':
                        return 'mm'
                      case '累计降雨量':
                        return 'mm'
                      case '水位':
                        return 'm'
                      case '来水流量':
                        return 'm³/s'
                      case '出库流量':
                        return 'm³/s'
                      default:
                        return
                    }
                  }
                  htmlStr += `
                    <div style="background:rgba(255,255,255,0.8); border-radius:4px 4px 4px 4px; padding:5px 8px; margin-top:4px; display:flex; align-items:center; justify-content:space-between">
                      <span style="margin-right:30px">
                        <span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:${color}"></span>
                        ${seriesName}
                      </span>
                      <span>
                        ${value} ${getUnit(seriesName)}
                      </span>
                    </div>
                    `
                }
                return htmlStr
              } else {
                return
              }
            },
            backgroundColor: '#F4F7FC ',
            borderWidth: 0,
            borderColor: '#cccccc',
            padding: 10,
            textStyle: {
              color: '#000',
              fontSize: 12,
              fontWeight: 500,
              align: 'left',
            },
          },
          legend: {
            show: true,
            x: 'center',
            y: '1',
            data: ['时段雨量', '雨量', '累计降雨量', '累计雨量', '来水流量', '出库流量', '水位'],

            textStyle: {
              fontSize: 12,
            },
          },
          // 将上下两个tootip合成一个
          axisPointer: {
            link: { xAxisIndex: 'all' },
          },
          xAxis: [
            {
              type: 'category',
              position: 'top',
              scale: true,
              axisLabel: {
                show: false,
              },
              axisTick: {
                alignWithLabel: true,
                show: false,
              },
              axisLine: {
                show: false,
              },
              splitLine: {
                show: false,
                lineStyle: {
                  type: 'dashed',
                  color: '#BBB',
                },
              },
            },
            {
              gridIndex: 1,
              type: 'category',
              scale: true,
              axisTick: {
                alignWithLabel: true,
                show: false,
              },
              axisLine: {
                show: false,
              },
            },
          ],
          yAxis: [
            {
              type: 'value',
              name: '时段雨量(mm)',
              nameLocation: 'middle',
              nameGap: 45,
              nameRotate: 270,
              inverse: true,
              nameTextStyle: {
                fontSize: 12,
                // padding: [0, 0, 0, -100], // 上右下左与原位置距离
              },
              axisLabel: {
                fontSize: 12,
              },
              splitLine: {
                show: false,
                lineStyle: {
                  type: 'dashed',
                  color: '#000',
                },
              },
              scale: true,
              splitLine: {
                show: true,
                lineStyle: {
                  type: 'dashed',
                  color: '#cccccc',
                },
              },
              alignTicks: true, // 配置多坐标轴标签对齐
            },
            {
              type: 'value',
              name: '累计降雨量(mm)',
              inverse: true,
              nameLocation: 'middle',
              nameGap: 45,
              nameRotate: 270,
              nameTextStyle: {
                // padding: [-140, 0, 0, 0], // 上右下左与原位置距离
                fontSize: 12,
              },
              axisLabel: {
                fontSize: 12,
              },
              scale: true,
              splitLine: {
                show: true,
                lineStyle: {
                  type: 'dashed',
                  color: '#cccccc',
                },
              },
              alignTicks: true, // 配置多坐标轴标签对齐
            },
            // {
            //   type: 'value',
            //   name: '水位(m)',
            //   nameLocation: 'middle',
            //   nameGap: 45,
            //   nameRotate: 270,
            //   nameTextStyle: {
            //     // padding: [0, 0, 0, -115], // 上右下左与原位置距离
            //     fontSize: 12,
            //   },
            //   gridIndex: 1,
            //   axisLabel: {
            //     fontSize: 12,
            //   },
            //   scale: true,
            //   splitLine: {
            //     show: false,
            //   },

            //   alignTicks: true, // 配置多坐标轴标签对齐
            // },
            {
              type: 'value',
              name: '流量(m³/s)',
              nameLocation: 'middle',
              nameGap: 45,
              nameRotate: 270,
              nameTextStyle: {
                // padding: [0, 0, 0, -100], // 上右下左与原位置距离
                fontSize: 12,
              },
              gridIndex: 1,
              axisLabel: {
                fontSize: 12,
              },
              scale: true,
              splitLine: {
                show: true,
                lineStyle: {
                  type: 'dashed',
                  color: '#cccccc',
                },
              },
              alignTicks: true, // 配置多坐标轴标签对齐
            },
          ],
          dataZoom: [
            {
              show: false,
              type: 'inside',
              xAxisIndex: [0, 1], // 显示 0 1 的数据，这个要加，不加的话，悬浮提示就会出问题
            },
          ],
          series: [], // 遍历动态填充
        }

        if (this.yAxis) {
          option.yAxis = this.yAxis
          option.yAxis.forEach(axis => {
            axis.gridIndex = 1
          })
        }
        dataSource.forEach((item, index) => {
          if (item.name === '时段雨量') {
            const seriesConfig = {
              name: '时段雨量',
              color: '#507EF7',
              type: 'bar',
              xAxisIndex: 0,
              yAxisIndex: 0,
              barMaxWidth: 10,
              showBackground: false,
              hoverAnimation: true, // 悬浮的动画加上
              data: item.data,
            }

            // 只有当markLineXAxis不为null时才添加预报依据时间标记线
            if (this.$attrs.markLineXAxis !== null && this.$attrs.markLineXAxis !== undefined) {
              seriesConfig.markLine = {
                lineStyle: {
                  width: 2,
                  color: 'red',
                },
                label: {
                  show: true,
                  position: 'start',
                  formatter: '预报依据时间',
                  color: '#32383B',
                  height: 10,
                  padding: [12, 12, 0, 12],
                },
                silent: true, // 鼠标悬停事件, true悬停不会出现实线
                symbol: 'none', // 去掉箭头
                data: [{ xAxis: this.$attrs.markLineXAxis }],
              }
            }

            option.series.push(seriesConfig)
          }

          // 处理'雨量'（自动预报页面使用的名称）
          if (item.name === '雨量') {
            const seriesConfig = {
              name: '雨量',
              color: '#507EF7',
              type: 'bar',
              xAxisIndex: 0,
              yAxisIndex: 0,
              barMaxWidth: 10,
              showBackground: false,
              hoverAnimation: true, // 悬浮的动画加上
              data: item.data,
            }

            // 只有当markLineXAxis不为null时才添加预报依据时间标记线
            if (this.$attrs.markLineXAxis !== null && this.$attrs.markLineXAxis !== undefined) {
              seriesConfig.markLine = {
                lineStyle: {
                  width: 2,
                  color: 'red',
                },
                label: {
                  show: true,
                  position: 'start',
                  formatter: '预报依据时间',
                  color: '#32383B',
                  height: 10,
                  padding: [12, 12, 0, 12],
                },
                silent: true, // 鼠标悬停事件, true悬停不会出现实线
                symbol: 'none', // 去掉箭头
                data: [{ xAxis: this.$attrs.markLineXAxis }],
              }
            }

            option.series.push(seriesConfig)
          }

          // 处理'降雨量'（来水反演页面使用的名称）
          if (item.name === '降雨量') {
            const seriesConfig = {
              name: '降雨量',
              color: '#507EF7',
              type: 'bar',
              xAxisIndex: 0,
              yAxisIndex: 0,
              barMaxWidth: 10,
              showBackground: false,
              hoverAnimation: true, // 悬浮的动画加上
              data: item.data,
            }

            // 只有当markLineXAxis不为null时才添加预报依据时间标记线
            if (this.$attrs.markLineXAxis !== null && this.$attrs.markLineXAxis !== undefined) {
              seriesConfig.markLine = {
                lineStyle: {
                  width: 2,
                  color: 'red',
                },
                label: {
                  show: true,
                  position: 'start',
                  formatter: '预报依据时间',
                  color: '#32383B',
                  height: 10,
                  padding: [12, 12, 0, 12],
                },
                silent: true, // 鼠标悬停事件, true悬停不会出现实线
                symbol: 'none', // 去掉箭头
                data: [{ xAxis: this.$attrs.markLineXAxis }],
              }
            }

            option.series.push(seriesConfig)
          }

          if (item.name === '累计降雨量') {
            option.series.push({
              name: '累计降雨量',
              color: '#74C3F8',
              type: 'line',
              xAxisIndex: 0,
              yAxisIndex: 1,
              smooth: true,
              symbol: 'none',
              showSymbol: true,
              symbolSize: 2,
              symbol: 'circle',
              hoverAnimation: true, // 悬浮的动画加上
              lineStyle: { width: 3 },
              data: item.data,
            })
          }

          if (item.name === '水位') {
            option.series.push({
              name: '水位',
              color: '#EF8432',
              type: 'line',
              xAxisIndex: 1,
              yAxisIndex: 2,
              smooth: true,
              symbol: 'none',
              showSymbol: true,
              symbolSize: 2,
              symbol: 'circle',
              hoverAnimation: true, // 悬浮的动画加上
              lineStyle: { width: 3 },
              markPoint: {
                symbolRotate: 180,
                symbolSize: 60,
                label: {
                  offset: [0, 13],
                },
                data: this.$attrs.markPointsData?.water || [],
              },
              data: item.data,
            })
          }

          if (item.name === '出库流量') {
            option.series.push({
              name: '出库流量',
              color: '#FF6B6B',
              type: 'line',
              xAxisIndex: 1,
              yAxisIndex: 2,
              smooth: true,
              symbol: 'none',
              showSymbol: true,
              symbolSize: 2,
              symbol: 'circle',
              hoverAnimation: true, // 悬浮的动画加上
              lineStyle: { width: 3 },
              data: item.data,
            })
          }

          if (item.name === '来水流量') {
            option.series.push({
              name: '来水流量',
              color: '#0FC6C2',
              type: 'line',
              xAxisIndex: 1,
              yAxisIndex: 2,
              smooth: true,
              symbol: 'none',
              showSymbol: true,
              symbolSize: 2,
              symbol: 'circle',
              hoverAnimation: true, // 悬浮的动画加上
              lineStyle: { width: 3 },
              markPoint: {
                symbolSize: 60,
                data: this.$attrs.markPointsData.flow,
              },
              data: item.data,
            })
          }
        })

        return option
      },
    },
  }
</script>
<style lang="less" scoped></style>
