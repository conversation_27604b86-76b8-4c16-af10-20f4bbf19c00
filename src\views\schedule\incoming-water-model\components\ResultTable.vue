<template>
  <VxeTable
    ref="vxeTableRef"
    :columns="columns"
    :tableData="$attrs.dataSource"
    tableTitle="预报结果"
    size="small"
    :tablePage="false"
    :isShowSetBtn="false"
    :isShowTableHeader="isShowTableHeader"
  ></VxeTable>
</template>

<script lang="jsx">
  import VxeTable from '@/components/VxeTable/index.vue'

  export default {
    name: 'ResultTable',

    components: {
      VxeTable,
    },
    props: {
      isShowTableHeader: {
        type: Boolean,
        default: true
      },
      // 场景类型：'forecast' - 来水预报，'inversion' - 来水反演
      scenario: {
        type: String,
        default: 'forecast',
        validator: value => ['forecast', 'inversion'].includes(value)
      }
    },
    data() {
      return {}
    },
    computed: {
      columns() {
        if (this.scenario === 'forecast') {
          return this.getForecastColumns()
        } else if (this.scenario === 'inversion') {
          return this.getInversionColumns()
        }
        return []
      }
    },
    methods: {
      getForecastColumns() {
        return [
          {
            title: '时间',
            field: 'tm',
            minWidth: 145,
          },
          {
            field: 'rain',
            minWidth: 90,
            align: 'center',
            slots: {
              header: () => (
                <div>
                  <div>时段雨量</div>
                  <span>(mm)</span>
                </div>
              ),
            },
          },
          {
            field: 'sumRain',
            minWidth: 90,
            align: 'center',
            slots: {
              header: () => (
                <div>
                  <div>累计降雨量</div>
                  <span>(mm)</span>
                </div>
              ),
            },
          },
          {
            field: 'inflow',
            minWidth: 100,
            align: 'center',
            slots: {
              header: () => (
                <div>
                  <div>预报来水流量</div>
                  <span>(m³/s)</span>
                </div>
              ),
            },
          },
          {
            field: 'inWater',
            minWidth: 100,
            align: 'center',
            slots: {
              header: () => (
                <div>
                  <div>累计来水量</div>
                  <span>(万m³)</span>
                </div>
              ),
            },
          },
        ]
      },

      getInversionColumns() {
        return [
          {
            title: '时间',
            field: 'tm',
            minWidth: 145,
          },
          {
            field: 'rain',
            minWidth: 90,
            align: 'center',
            slots: {
              header: () => (
                <div>
                  <div>时段雨量</div>
                  <span>(mm)</span>
                </div>
              ),
            },
          },
          {
            field: 'sumRain',
            minWidth: 90,
            align: 'center',
            slots: {
              header: () => (
                <div>
                  <div>累计降雨量</div>
                  <span>(mm)</span>
                </div>
              ),
            },
          },
          {
            field: 'inflow',
            minWidth: 100,
            align: 'center',
            slots: {
              header: () => (
                <div>
                  <div>来水流量</div>
                  <span>(m³/s)</span>
                </div>
              ),
            },
          },
          {
            field: 'outflow',
            minWidth: 100,
            align: 'center',
            slots: {
              header: () => (
                <div>
                  <div>出库流量</div>
                  <span>(m³/s)</span>
                </div>
              ),
              default: ({ row }) => {
                return this.formatOutflow(row.outflow)
              }
            },
          },
          {
            field: 'wlv',
            minWidth: 70,
            align: 'center',
            slots: {
              header: () => (
                <div>
                  <div>水位</div>
                  <span>(m)</span>
                </div>
              ),
            },
          },
          {
            field: 'storageValue',
            minWidth: 100,
            align: 'center',
            slots: {
              header: () => (
                <div>
                  <div>库容</div>
                  <span>(万m³)</span>
                </div>
              ),
            },
          },
          {
            field: 'storageChange',
            minWidth: 100,
            align: 'center',
            slots: {
              header: () => (
                <div>
                  <div>库容变化量</div>
                  <span>(m³)</span>
                </div>
              ),
              default: ({ row }) => {
                return this.formatStorageChange(row.storageChange)
              }
            },
          },
        ]
      },

      // 格式化出库流量，保留三位小数
      formatOutflow(value) {
        if (value === null || value === undefined || value === '') {
          return '-'
        }
        const num = Number(value)
        if (isNaN(num)) {
          return '-'
        }
        // 如果是整数，直接返回
        if (num % 1 === 0) {
          return num.toString()
        }
        // 如果是小数，保留三位小数
        return num.toFixed(3)
      },

      // 格式化库容变化量
      formatStorageChange(value) {
        if (value === null || value === undefined || value === '') {
          return '-'
        }
        const num = Number(value)
        if (isNaN(num)) {
          return '-'
        }
        return num.toString()
      }
    }
  }
</script>

<style lang="less" scoped></style>
